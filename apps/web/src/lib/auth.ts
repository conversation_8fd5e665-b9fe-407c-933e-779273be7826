import { betterAuth } from 'better-auth';
import { prismaAdapter } from 'better-auth/adapters/prisma';
import { createAuthMiddleware, APIError } from 'better-auth/api';
import { prisma } from './prisma';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const auth: any = betterAuth({
  appName: 'startwrite',
  database: prismaAdapter(prisma, {
    provider: 'postgresql',
  }),
  emailAndPassword: {
    enabled: true,
  },
  baseURL: process.env.VERCEL_URL ? 'https://' + process.env.VERCEL_URL : 'http://localhost:3000',
  basePath: '/api/auth',
  trustedOrigins: ['http://localhost:3000', 'http://localhost:3001'],
  hooks: {
    before: createAuthMiddleware(async (ctx) => {
      // Log authentication attempts for security monitoring
      if (ctx.path === '/sign-in/email') {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        console.log(`Sign-in attempt for email: ${(ctx.body as any)?.email}`);
      }
      
      // Validate email format for sign-up
      if (ctx.path === '/sign-up/email') {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const email = (ctx.body as any)?.email;
        if (email && !email.includes('@')) {
          throw new APIError('BAD_REQUEST', {
            message: 'Invalid email format',
          });
        }
        console.log(`Sign-up attempt for email: ${email}`);
      }
    }),
    after: createAuthMiddleware(async (ctx) => {
      // Log successful authentication events
      if (ctx.path.startsWith('/sign-in') && ctx.context.newSession) {
        const session = ctx.context.newSession;
        console.log(`Successful sign-in for user: ${session.user.email}`);
      }
      
      // Log new user registrations
      if (ctx.path.startsWith('/sign-up') && ctx.context.newSession) {
        const session = ctx.context.newSession;
        console.log(`New user registered: ${session.user.email}`);
        // Here you could add additional logic like:
        // - Send welcome email
        // - Create user profile
        // - Log to analytics
      }
      
      // Log sign-out events
      if (ctx.path === '/sign-out') {
        console.log('User signed out successfully');
      }
    }),
  },
// eslint-disable-next-line @typescript-eslint/no-explicit-any
}) as any;
